# Generic Knowledge Extraction AI Agent

This document outlines the development of a generic knowledge extraction AI agent that extracts structured data from unstructured documents, enabling organization-specific use cases without requiring programming expertise.

## Overview
The agent allows users to define extraction tasks in plain language, automatically generating dynamic Pydantic data models and extracting knowledge using either Anthropic's Claude or OpenAI's models, with optional Azure endpoint integration for secure processing. The system is reusable, modifiable, and accessible to non-technical users via a Streamlit-based interface.

## Core Components
1. **User Interface (ui_app.py)**  
   - Streamlit web app for defining extraction requirements, selecting AI models, and managing workflows without coding.
   - Supports use case creation/loading and Azure integration.

2. **API Clients (claude_client.py, openai_client.py)**  
   - Handle authentication and model selection for Claude and OpenAI.
   - OpenAI client supports Azure endpoints for enterprise security.

3. **Model Generator (model_generator.py)**  
   - Converts plain language field descriptions into Pydantic data models with validation, enums, and type hints.
   - Uses Claude-Sonnet-4 for model generation and GPT-4 for extraction by default.

4. **Document Parser (document_parser.py)**  
   - Processes PDF, DOCX, and DOC files using PyMuPDF and python-docx.
   - Supports batch processing with error handling.

5. **Knowledge Extractors (claude_extractor.py, openai_extractor.py)**  
   - Perform extraction using Claude or OpenAI models.
   - Support batch processing, error handling, and Azure endpoints.

## Workflow
1. **Configuration**  
   - Users define fields, select models, and configure Azure settings via the UI.
   - Configurations saved as JSON (e.g., config19.json) with Pydantic models (models.py) and prompts (prompt.py).

2. **Model Generation**  
   - Converts field descriptions into Pydantic models using selected LLM.
   - Handles enums and validation, with fallback for invalid code.

3. **Document Processing**  
   - Parses documents using PyMuPDF (PDF) or python-docx (DOCX/DOC).
   - Supports batch processing with error logging.

4. **Knowledge Extraction**  
   - Executes extraction using Claude or OpenAI, validating output with Pydantic.
   - Exports results in JSON, CSV, or Excel.

5. **Reusability**  
   - Stores use cases for reuse and modification.
   - Supports organizational templates for various document types.

## Example Use Case: AI Reports Extraction
```json
{
  "extraction_config": {
    "use_case": "AI reports extraction",
    "description": "Extracting structured information from unstructured AI consultancy documents",
    "main_model_name": "testInfo",
    "document_type": "AI consultancy documents",
    "fields": [
      {
        "field_name": "company",
        "field_type": "str",
        "description": "name of the company",
        "required": true,
        "enum_values": null
      },
      {
        "field_name": "ai_field",
        "field_type": "enum",
        "description": "The primary AI field the company is using or planning to use",
        "required": true,
        "enum_values": ["Generative AI", "Machine learning", "Predictive analytics", "Computer vision & image processing", "Rule-based systems", "Other"]
      }
    ]
  }
}
```

## Example Pydantic Model
```python
from pydantic import BaseModel, Field
from enum import Enum
from typing import Optional

class Ai_Field(str, Enum):
    GENERATIVE_AI = "Generative AI"
    MACHINE_LEARNING = "Machine learning"
    PREDICTIVE_ANALYTICS = "Predictive analytics"
    COMPUTER_VISION = "Computer vision & image processing"
    RULE_BASED = "Rule-based systems"
    OTHER = "Other"

class testInfo(BaseModel):
    company: str = Field(..., description="name of the company")
    ai_field: Ai_Field = Field(..., description="The primary AI field the company is using or planning to use")
```

## Testing
Tested for extracting data from invoices, research papers, clinical lab reports, and resumes. Example: Clinical lab reports extracted test name, result, reference range, and conclusion (normal/abnormal/borderline) from 8 documents.

## Implementation Notes
- **Security**: Azure integration ensures compliance for enterprise use.
- **Flexibility**: Supports Claude and OpenAI models, with customizable configurations.
- **Accessibility**: No-code interface enables non-technical users to create and manage extraction tasks.
- **Code Availability**: Full code with guidelines available on GitHub.