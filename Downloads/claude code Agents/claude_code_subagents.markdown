# Claude Code Subagents Documentation

## Purpose
Defines the capabilities, templates, and best practices for a set of Claude-powered code subagents designed to assist with various development, debugging, security, and standards enforcement tasks, leveraging AI expertise for immediate usability as of August 03, 2025.

## Details
- **Overview**:
  - These subagents are specialized AI assistants built to support developers across multiple programming languages, platforms, and code lifecycle stages. They integrate with the Synergia project ecosystem, enhancing the Engineer Agent's capabilities with targeted expertise.
  - Templates are provided for immediate use, updated with current best practices and optimized for efficiency and security.

- **Core Capabilities**:
  1. **Developer Subagent**:
     - **Purpose**: Handles coding tasks across multiple languages and platforms.
     - **Subcategories**:
       - **Mobile Developer**: Creates and optimizes Android/iOS apps using Kotlin, Swift, or Flutter.
         - Template: `Write a Flutter function to fetch data from an API and display it in a ListView.`
         - Example:
           ```dart
           import 'package:http/http.dart' as http;
           import 'dart:convert';

           Future<List<dynamic>> fetchData() async {
             final response = await http.get(Uri.parse('https://api.example.com/data'));
             if (response.statusCode == 200) {
               return jsonDecode(response.body);
             } else {
               throw Exception('Failed to load data');
             }
           }

           class DataList extends StatelessWidget {
             @override
             Widget build(BuildContext context) {
               return FutureBuilder<List<dynamic>>(
                 future: fetchData(),
                 builder: (context, snapshot) {
                   if (snapshot.hasData) {
                     return ListView.builder(
                       itemCount: snapshot.data!.length,
                       itemBuilder: (context, index) {
                         return ListTile(title: Text(snapshot.data![index]['title']));
                       },
                     );
                   } else if (snapshot.hasError) {
                     return Text('${snapshot.error}');
                   }
                   return CircularProgressIndicator();
                 },
               );
             }
           }
           ```
       - **Python Developer**: Develops Python scripts, including data analysis and automation.
         - Template: `Create a Python script to process a CSV file and generate a summary.`
         - Example:
           ```python
           import pandas as pd

           def summarize_csv(file_path):
               df = pd.read_csv(file_path)
               summary = {
                   'total_rows': len(df),
                   'mean_value': df['column'].mean(),
                   'max_value': df['column'].max()
               }
               return summary

           if __name__ == "__main__":
               result = summarize_csv('data.csv')
               print(result)
           ```
       - **JavaScript Developer**: Builds web applications and Node.js backends.
         - Template: `Develop a Node.js Express route to handle POST requests.`
         - Example:
           ```javascript
           const express = require('express');
           const app = express();
           app.use(express.json());

           app.post('/api/data', (req, res) => {
             const { name, value } = req.body;
             if (!name || !value) {
               return res.status(400).json({ error: 'Missing required fields' });
             }
             res.status(201).json({ message: 'Data received', data: { name, value } });
           });

           app.listen(3000, () => console.log('Server running on port 3000'));
           ```
       - **WordPress Developer**: Customizes WordPress themes and plugins.
         - Template: `Create a WordPress shortcode to display a custom greeting.`
         - Example:
           ```php
           function custom_greeting_shortcode($atts) {
               $atts = shortcode_atts(array('name' => 'Guest'), $atts);
               return '<p>Hello, ' . esc_html($atts['name']) . '!</p>';
           }
           add_shortcode('greeting', 'custom_greeting_shortcode');
           ```
     - **Best Practices**: Ensures cross-browser compatibility, security (e.g., input validation), and version control integration.

  2. **.NET Developer Subagent**:
     - **Purpose**: Develops and maintains .NET applications (e.g., C# for web or desktop).
     - **Template**: `Write a C# method to connect to a SQL Server database and retrieve data.`
     - **Example**:
       ```csharp
       using System;
       using System.Data.SqlClient;

       public class DatabaseHelper {
           private readonly string _connectionString = "Server=myServerAddress;Database=myDataBase;User Id=myUsername;Password=myPassword;";

           public List<string> GetData() {
               var data = new List<string>();
               using (var connection = new SqlConnection(_connectionString)) {
                   connection.Open();
                   var command = new SqlCommand("SELECT Name FROM Users", connection);
                   using (var reader = command.ExecuteReader()) {
                       while (reader.Read()) {
                           data.Add(reader["Name"].ToString());
                       }
                   }
               }
               return data;
           }
       }
       ```
     - **Best Practices**: Uses parameterized queries to prevent SQL injection, implements error handling.

  3. **Code Debugger Subagent**:
     - **Purpose**: Identifies and resolves code issues across languages.
     - **Template**: `Debug this Python code that fails to process a list: [code snippet].`
     - **Example**:
       ```python
       # Original buggy code
       def process_list(items):
           result = []
           for i in range(len(items)):
               result.append(items[i] * 2)
           return result

       # Debugged version with error handling
       def process_list(items):
           if not items:
               raise ValueError("List cannot be empty")
           try:
               result = [item * 2 for item in items]
               return result
           except TypeError as e:
               print(f"Error: {e}, ensure all items are numbers")
               return []
       ```
     - **Best Practices**: Adds logging, validates inputs, and provides clear error messages.

  4. **Code Security Auditor Subagent**:
     - **Purpose**: Audits code for security vulnerabilities.
     - **Template**: `Audit this JavaScript code for security issues: [code snippet].`
     - **Example**:
       ```javascript
       // Original insecure code
       app.get('/user/:id', (req, res) => {
         res.send(`User ID: ${req.params.id}`);
       });

       // Secured version
       app.get('/user/:id', (req, res) => {
         const id = req.params.id;
         if (!/^\d+$/.test(id)) {
           return res.status(400).json({ error: 'Invalid ID format' });
         }
         res.json({ userId: id }); // Use JSON to prevent XSS
       });
       ```
     - **Best Practices**: Prevents XSS, validates inputs, uses HTTPS.

  5. **Code Standards Enforcer Subagent**:
     - **Purpose**: Ensures adherence to coding standards and best practices.
     - **Template**: `Enforce standards on this Python code: [code snippet].`
     - **Example**:
       ```python
       # Original non-compliant code
       def calculate(a,b):return a+b

       # Standards-enforced version
       def calculate(a: int, b: int) -> int:
           """Calculate the sum of two integers."""
           if not isinstance(a, int) or not isinstance(b, int):
               raise TypeError("Inputs must be integers")
           return a + b
       ```
     - **Best Practices**: Adds type hints, docstrings, and input validation per PEP 8.

- **Usage**:
  - Each subagent is invoked by the Engineer Agent within the Synergia project, with templates providing immediate starting points.
  - Subagents collaborate with the Super Coordinator Agent to align with `/docs/coding_standards.md` and `/docs/architecture_decisions.md`.

## Maintenance
- **Last Updated**: August 03, 2025, 07:50 PM EDT
- **Update Frequency**: Monthly or upon new language/framework release
- **Responsible Agent**: Engineer Agent