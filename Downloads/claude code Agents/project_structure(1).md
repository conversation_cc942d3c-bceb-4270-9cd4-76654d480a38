# Project Structure Documentation

## Purpose
Defines the file organization and directory structure for the AI agent system to ensure maintainability and accessibility.

## Details
- **Directory Structure**:
  ```
  /docs/
  ├── implementation.md          # Current tasks and progress
  ├── bug_tracking.md           # Known issues and solutions
  ├── project_structure.md      # File organization rules
  ├── ui_ux_doc.md             # Design patterns and components
  ├── api_documentation.md      # API specifications
  ├── testing_requirements.md   # Testing standards
  ├── coding_standards.md       # Code style and conventions
  ├── architecture_decisions.md # Technical architecture rationale
  /src/
  ├── backend/                 # ElysiaJS, SQLite, Prisma code
  ├── frontend/                # React 19, Tailwind CSS code
  ├── tests/                   # Backend test files
  ```
- **File Naming Conventions**:
  - Use lowercase with hyphens (e.g., `ui_ux_doc.md`).
  - Prefix critical files with `/docs/` in references.
- **Access Rules**:
  - All agents read from `/docs/` before task initiation.
  - Super Coordinator Agent maintains structure integrity.

## Maintenance
- **Last Updated**: July 30, 2025
- **Update Frequency**: Annually or upon structural change
- **Responsible Agent**: Super Coordinator Agent