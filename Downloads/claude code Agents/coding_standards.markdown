# Coding Standards Documentation

## Purpose
Establishes code style, formatting, and best practices for consistent development.

## Details
- **Language Rules**:
  - **JavaScript/TS**: Use ESLint with <PERSON><PERSON><PERSON>.
  - **Naming**: camelCase for variables, PascalCase for classes/components.
- **Formatting**:
  - Indentation: 2 spaces.
  - Max line length: 100 characters.
- **Best Practices**:
  - Avoid global variables.
  - Use const/let over var.
  - Comment complex logic with purpose.
- **ElysiaJS Specifics**:
  - Type-safe routes with TypeScript.
  - Modularize handlers and middleware.
- **Commit Messages**:
  - Format: `[type] Short description` (e.g., `[feat] Add product API`).

## Maintenance
- **Last Updated**: July 30, 2025
- **Update Frequency**: Annually or upon new standard
- **Responsible Agent**: Engineer Agent