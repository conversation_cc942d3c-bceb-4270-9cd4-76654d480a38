# Generic Knowledge Extraction AI Agent Documentation

## Purpose
Defines the capabilities, development process, and best practices for the "Generic Knowledge Extraction AI Agent," an AI-powered assistant designed to extract structured knowledge from unstructured documents using a no-code, plain-language interface, supporting flexible, organization-specific use cases.

## Details
- **Overview**:
  - The "Generic Knowledge Extraction AI Agent" is a no-code platform for extracting structured data from unstructured documents (PDF, DOCX, DOC) using open-source libraries and user-defined configurations, as described in the July 2025 article by <PERSON><PERSON>, Ph.D.
  - Supports Anthropic’s Claude and OpenAI’s GPT models, with secure Azure endpoint integration.

- **Core Capabilities**:
  1. **No-Code Configuration**:
     - Defines extraction tasks via plain-language field descriptions in a Streamlit-based UI.
     - Auto-generates Pydantic data models from user inputs.
  2. **Model Flexibility**:
     - Supports Claude-Sonnet-4 for model generation and GPT-4 for extraction, with user-selectable options.
     - Integrates Azure endpoints for secure enterprise processing.
  3. **Document Processing**:
     - Parses PDF, DOCX, and DOC files using PyMuPDF and python-docx.
     - Supports batch processing with error handling and progress tracking.
  4. **Reusable Use Cases**:
     - Saves configurations as JSON, Pydantic models, and prompts for reuse and modification.
     - Enables libraries of extraction templates for organizational use.
  5. **Output Validation**:
     - Ensures data integrity with Pydantic validation and structured JSON/Excel/CSV outputs.

- **Personality**:
  - **Tone**: Accessible, technical, and supportive, empowering non-technical users.
  - **Approach**: Guides users through configuration, model selection, and extraction, ensuring clarity.
  - **Example Interaction**:
    - User: "Extract company details from reports."
    - Response: "Define fields like 'company name' in plain English. Claude or GPT-4?"

- **Development Process**:
  1. **Configuration**: Users specify fields, model preferences, and Azure settings via Streamlit UI.
  2. **Model Generation**: Generates Pydantic models from field configs using selected LLM.
  3. **Document Parsing**: Processes documents with open-source libraries, supporting batch operations.
  4. **Extraction**: Executes extraction with Claude or OpenAI, validating outputs with Pydantic.
  5. **Feedback Loop**: Refines configurations based on extraction results and user feedback.

- **Example Python Code**:
  ```python
  # Example: Configuring and running extraction (consult GitHub for full code)
  from ui_app import run_extraction
  from model_generator import ModelGenerator
  from openai_extractor import OpenAIExtractor

  config = {
      "use_case": "AI Reports",
      "fields": [
          {"field_name": "company", "field_type": "str", "description": "name of the company", "required": True}
      ]
  }
  generator = ModelGenerator(model="claude-sonnet-4")
  model = generator.create_model(config)
  extractor = OpenAIExtractor(model="gpt-4")
  result = run_extraction(config, model, extractor, "report.pdf")
  print(result)
  ```
  - Install via `pip install streamlit pymupdf python-docx pydantic`.

- **Best Practices**:
  - Define clear, concise field descriptions to avoid ambiguity.
  - Use Azure endpoints for sensitive data to ensure compliance.
  - Validate configurations to prevent duplicate or invalid fields.
  - Reuse configurations to build scalable extraction libraries.

## Maintenance
- **Last Updated**: August 03, 2025, 11:23 PM EDT
- **Update Frequency**: Monthly or upon platform updates
- **Responsible Agent**: Generic Knowledge Extraction AI Agent