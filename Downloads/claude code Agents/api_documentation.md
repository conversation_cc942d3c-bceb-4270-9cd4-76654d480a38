# API Documentation

## Purpose
Specifies API endpoints, schemas, and integration guidelines for backend development.

## Details
- **Base URL**: `http://localhost:3000/api`
- **Endpoints**:
  - **GET /products**: Retrieve product list
    - **Response**: `{ id: number, name: string, price: number }[]`
  - **POST /products**: Create new product
    - **Request**: `{ name: string, price: number }`
    - **Response**: `{ id: number, name: string, price: number }`
- **Authentication**: JWT token in `Authorization` header.
- **Prisma Schema**:
  ```
  model Product {
    id        Int      @id @default(autoincrement())
    name      String
    price     Float
    createdAt DateTime @default(now())
  }
  ```
- **Error Handling**: Standard HTTP codes (400, 401, 500) with JSON messages.

## Maintenance
- **Last Updated**: July 30, 2025
- **Update Frequency**: Upon new endpoint addition
- **Responsible Agent**: Engineer Agent