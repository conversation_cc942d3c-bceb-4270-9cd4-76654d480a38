# Get Started with Genkit Agent

This guide demonstrates how to set up a Genkit-based AI agent in a Node.js application and test it using the Developer UI.

## Prerequisites
- Node.js v20 or later
- npm
- Familiarity with Node.js development

## Setup
Create a new Node.js project with TypeScript:

```bash
mkdir my-genkit-agent
cd my-genkit-agent
npm init -y
mkdir src
touch src/index.ts
npm install -D typescript tsx
npx tsc --init
```

## Install Genkit
Install the Genkit CLI and required packages:

```bash
npm install -g genkit-cli
npm install genkit @genkit-ai/googleai
```

- `genkit`: Core Genkit functionality
- `@genkit-ai/googleai`: Google AI Gemini model integration

## API Key
Obtain a Gemini API key from [Google AI Studio](https://aistudio.google.com/) and set it:

```bash
export GEMINI_API_KEY=<your-api-key>
```

## Create an Agent Flow
Define a Genkit flow for an AI agent in `src/index.ts`:

```javascript
import { googleAI } from '@genkit-ai/googleai';
import { genkit, z } from 'genkit';

// Initialize Genkit with Google AI plugin
const ai = genkit({
  plugins: [googleAI()],
  model: googleAI.model('gemini-2.5-flash', { temperature: 0.7 }),
});

// Input schema
const AgentInputSchema = z.object({
  query: z.string().describe('User query or task'),
  context: z.string().optional().describe('Additional context for the agent'),
});

// Output schema
const AgentOutputSchema = z.object({
  response: z.string().describe('Agent’s response to the query'),
  actions: z.array(z.string()).optional().describe('Suggested actions'),
});

// Define agent flow
export const agentFlow = ai.defineFlow(
  {
    name: 'agentFlow',
    inputSchema: AgentInputSchema,
    outputSchema: AgentOutputSchema,
  },
  async (input) => {
    const prompt = `Act as an intelligent agent. Respond to the query: "${input.query}". Context: ${input.context || 'none'}. Provide a concise response and optional actions.`;
    
    const { output } = await ai.generate({
      prompt,
      output: { schema: AgentOutputSchema },
    });

    if (!output) throw new Error('Failed to generate response');
    return output;
  }
);

// Run the flow
async function main() {
  const result = await agentFlow({
    query: 'Plan a weekend trip to Paris',
    context: 'Budget-friendly, interested in art and food',
  });
  console.log(result);
}

main().catch(console.error);
```

This code:
- Defines input/output schemas with Zod
- Configures `gemini-2.5-flash` with a temperature of 0.7
- Creates an agent flow for query-based responses
- Runs a sample query with context

## Benefits
- Type-safe schemas
- Developer UI integration
- Observable flows
- Easy API deployment

## Test with Developer UI
Start the Developer UI:

```bash
genkit start -- npx tsx --watch src/index.ts
```

Add to `package.json`:

```json
"scripts": {
  "genkit:ui": "genkit start -- npx tsx --watch src/index.ts"
}
```

Run:

```bash
npm run genkit:ui
```

In the Developer UI (`http://localhost:4000`):
1. Select `agentFlow`
2. Enter input:
   ```json
   {
     "query": "Plan a weekend trip to Paris",
     "context": "Budget-friendly, interested in art and food"
   }
   ```
3. Click Run to view the response and trace.

## Next Steps
- Explore [Genkit CLI and Developer UI](https://genkit.dev/docs/developer-tools)
- Learn about [content generation](https://genkit.dev/docs/generating-content)
- Customize [flows and schemas](https://genkit.dev/docs/defining-flows)
- Manage [prompts](https://genkit.dev/docs/prompt-management)
- Build a [full-stack app](https://genkit.dev/docs/app-integration)