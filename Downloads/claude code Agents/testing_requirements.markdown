# Testing Requirements Documentation

## Purpose
Outlines testing standards and requirements for ensuring code quality and functionality.

## Details
- **Backend Testing**:
  - **Mandatory**: Test all ElysiaJS endpoints (unit and integration).
  - **Coverage**: Aim for > 90% line coverage.
  - **Tools**: Use Jest with Prisma testing library.
  - **Example**:
    ```javascript
    test('GET /products returns list', async () => {
      const res = await request(app).get('/products');
      expect(res.status).toBe(200);
      expect(res.body).toBeInstanceOf(Array);
    });
    ```
- **Frontend Testing**:
  - **Optional**: Test complex React components (e.g., forms, modals).
  - **Tools**: React Testing Library, optional Vitest.
- **Quality Gates**:
  - No failing tests before merging.
  - Review test coverage with Super Coordinator.

## Maintenance
- **Last Updated**: July 30, 2025
- **Update Frequency**: Upon new feature or tool addition
- **Responsible Agent**: Engineer Agent